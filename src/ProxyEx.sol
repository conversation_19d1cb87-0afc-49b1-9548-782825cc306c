// SPDX-License-Identifier: UNLICENSED

import "@openzeppelin/contracts/access/Ownable.sol";
//import "./openzeppelin/Ownable.sol";
//import "hardhat/console.sol"; //debug

pragma solidity ^0.8.0;

interface IPair {
    function token0() external view returns (address);
    function token1() external view returns (address);
    function getReserves() external view returns (uint112 reserve0, uint112 reserve1, uint32 blockTimestampLast);
    function swap(uint256 amount0Out, uint256 amount1Out, address to, bytes calldata data) external;
    function skim(address to) external;
    //for stable
    function stable() external view returns (bool);
    function getAmountOut(uint256 amountIn, address tokenIn) external view returns (uint256);
}

pragma solidity ^0.8.0;

interface IPairV1 {
    function token0() external view returns (address);
    function token1() external view returns (address);
    function getReserves() external view returns (uint112 reserve0, uint112 reserve1);
    function swap(uint256 amount0Out, uint256 amount1Out, address to) external;
    function skim(address to) external;
}

pragma solidity ^0.8.0;

interface IRouter {
    function factory() external view returns (address);
    function factoryV2() external view returns (address);
    function swapExactTokensForTokens(
        uint256 amountIn,
        uint256 amountOutMin,
        address[] calldata path,
        address to,
        uint256 deadline
    ) external returns (uint256[] memory amounts);
    function swapExactTokensForTokensSupportingFeeOnTransferTokens(
        uint256 amountIn,
        uint256 amountOutMin,
        address[] calldata path,
        address to,
        uint256 deadline
    ) external;
}

pragma solidity ^0.8.0;

interface IFactory {
    function allPairsLength() external view returns (uint256);
    function totalPairs() external view returns (uint256);
    function allPairs(uint256 index) external view returns (address);
}

pragma solidity ^0.8.0;

// File: @openzeppelin/contracts/token/ERC20/IERC20.sol
/**
 * @dev Interface of the ERC20 standard as defined in the EIP.
 */
interface IERC20 {
    function symbol() external view returns (string memory);
    function decimals() external view returns (uint256);
    function totalSupply() external view returns (uint256);
    function balanceOf(address account) external view returns (uint256);
    function transfer(address recipient, uint256 amount) external returns (bool);
    function allowance(address owner, address spender) external view returns (uint256);
    function approve(address spender, uint256 amount) external returns (bool);
    function transferFrom(address sender, address recipient, uint256 amount) external returns (bool);
    function withdraw(uint256 wad) external payable;
    function deposit() external payable;

    event Transfer(address indexed from, address indexed to, uint256 value);
    event Approval(address indexed owner, address indexed spender, uint256 value);
}

// File: @openzeppelin/contracts/access/Roles.sol
pragma solidity ^0.8.0;

library Roles {
    struct Role {
        mapping(address => bool) bearer;
    }

    function add(Role storage role, address account) internal {
        require(!has(role, account), "Roles: account already has role");
        role.bearer[account] = true;
    }

    function remove(Role storage role, address account) internal {
        require(has(role, account), "Roles: account does not have role");
        role.bearer[account] = false;
    }

    function has(Role storage role, address account) internal view returns (bool) {
        require(account != address(0), "Roles: account is the zero address");
        return role.bearer[account];
    }
}

pragma solidity ^0.8.0;
//pragma experimental ABIEncoderV2;

contract ProxyEx is Ownable {
    using Roles for Roles.Role;

    mapping(address => PairDataLocal) pairDatas;
    Roles.Role private _operators;

    address public logicContact;
    address public uniSwapRouter;

    mapping(address => uint256) tokenDecimals;
    mapping(address => uint256) goldenSectionCfg;

    uint8 findMaxStep = 30;
    uint16 findMaxEps = 250;
    uint112 constant MAX_UINT112 = 5192296858534827628530496329220095;

    address vault; //金库

    struct FeedList {
        address addr;
        uint256 min;
        uint256 max;
        uint256 balance;
        uint256 feedAmount;
    }

    //check allowance
    struct Whale {
        address addr;
        address token;
        uint256 amount;
        bool isEth;
        bool isGreater;
        address spender;
        address spenderToken;
        uint256 spenderAmount;
    }

    struct SwapData {
        address tokenIn;
        uint256 sumIn;
        uint256[] amountsIn;
        address[][] pairs;
        uint256 minOut;
    }

    struct PairDataLocal {
        address token0;
        address token1;
        uint256 r0;
        uint256 r1;
    } //uint fee0; //uint fee1;

    struct PairData {
        address addr;
        uint256 version;
        uint256 fee; //只需记录单个方向的fee
        uint256 fp; //pair fee
    }

    //PairData和PairDataLocal的组合
    struct PairDataFull {
        address addr;
        uint256 version;
        uint256 fee;
        uint256 fp; //pair fee
        address token0;
        address token1;
        uint256 r0;
        uint256 r1;
        uint256 d0;
        uint256 d1;
    }

    constructor() Ownable(msg.sender) {
        _operators.add(msg.sender);
        _operators.add(address(this));
    }

    modifier onlyOperator() {
        require(_operators.has(msg.sender), "Operators: caller is not the Operator");
        _;
    }

    /**
     * operate begin **************
     */

    function addOpts(address[] memory operators) external onlyOwner {
        for (uint256 i = 0; i < operators.length; ++i) {
            if (!isOperator(operators[i])) _operators.add(operators[i]);
        }
    }

    function removeOperators(address operator) external onlyOwner {
        _operators.remove(operator);
    }

    function isOperator(address operator) public view returns (bool) {
        return _operators.has(operator);
    }

    /**
     * operate end **************
     */

    //golden config
    function setGoldenStep(uint8 step, uint16 eps) external onlyOwner {
        findMaxStep = step;
        findMaxEps = eps;
    }

    function setGoldenCfg(address token, uint256 tolerance) external onlyOwner {
        goldenSectionCfg[token] = tolerance;
    }

    function getGoldenCfg(address token) external view returns (uint256) {
        return goldenSectionCfg[token];
    }

    function getBalance(address token) public view returns (uint256) {
        return IERC20(token).balanceOf(address(this));
    }

    function getBalanceOf(address holder, address token) public view returns (uint256) {
        return IERC20(token).balanceOf(holder);
    }
    /**
     * manager begin **************
     */

    function withdrawToEx(address token, address to, uint256 amount) external onlyOwner {
        IERC20(token).transfer(to, amount);
    }

    function withdrawAllEx(address token) external onlyOwner {
        IERC20(token).transfer(msg.sender, IERC20(token).balanceOf(address(this)));
    }

    function withdrawAllToEx(address[] memory tokens, address to) external onlyOwner {
        for (uint256 i; i < tokens.length; i++) {
            IERC20 token = IERC20(tokens[i]);
            uint256 balance = token.balanceOf(address(this));
            if (balance > 0) {
                token.transfer(to, balance);
            }
        }
    }

    function withdrawAllEthEx() external payable onlyOwner {
        payable(msg.sender).transfer(address(this).balance);
    }

    /**
     * manager end **************
     */

    function setLogicContact(address _new) external onlyOwner {
        logicContact = _new;
    }

    function setRouter(address _new) external onlyOwner {
        uniSwapRouter = _new;
    }

    /**
     * feed begin **************
     */

    function checkFeed(FeedList[] memory feedList) public view returns (FeedList[] memory, uint256) {
        uint256 total = 0;
        for (uint256 i; i < feedList.length; i++) {
            uint256 balance = feedList[i].addr.balance;
            feedList[i].balance = balance;
            if (balance < feedList[i].min) {
                uint256 amount = feedList[i].max - balance;
                total = amount + total;
                feedList[i].feedAmount = amount;
            }
        }
        return (feedList, total);
    }

    function feed(FeedList[] calldata feedList, address eth, uint256 total) external payable onlyOperator {
        uint256 balance = getBalance(eth);
        require(balance > total, "not enough balance");
        IERC20(eth).withdraw(total);
        for (uint256 i; i < feedList.length; i++) {
            //(bool sent, bytes memory data)
            (bool sent,) = feedList[i].addr.call{value: feedList[i].feedAmount}("");
            require(sent, "Failed to send Ether");
        }
    }

    /**
     * feed end **************
     */

    /**
     * util begin **************
     */
    // babylonian method (https://en.wikipedia.org/wiki/Methods_of_computing_square_roots#Babylonian_method)
    function sqrt(uint256 y) internal pure returns (uint256 z) {
        if (y > 3) {
            z = y;
            uint256 x = y / 2 + 1;
            while (x < z) {
                z = x;
                x = (y / x + x) / 2;
            }
        } else if (y != 0) {
            z = 1;
        }
    }

    function verifyWhale(Whale memory whale) internal view returns (bool) {
        if (whale.addr == address(0)) return true;
        if (whale.addr != address(0)) {
            uint256 balance = whale.isEth ? whale.addr.balance : IERC20(whale.token).balanceOf(whale.addr);
            if (whale.isGreater && balance >= whale.amount && balance > 0) return true;
            if (!whale.isGreater && balance <= whale.amount) return true;
        }
        if (whale.spender != address(0)) {
            //check allowance
            if (IERC20(whale.spenderToken).allowance(whale.addr, whale.spender) >= whale.spenderAmount) return true;
        }
        return false;
    }

    function sortTokens(address tokenA, address tokenB) internal pure returns (address token0, address token1) {
        require(tokenA != tokenB, "KKK: IDENTICAL_ADDRESSES");
        (token0, token1) = tokenA < tokenB ? (tokenA, tokenB) : (tokenB, tokenA);
        require(token0 != address(0), "KKK: ZERO_ADDRESS");
    }

    function getAmountOutWithFee(uint256 amountIn, uint256 reserveIn, uint256 reserveOut, uint256 fee)
        internal
        pure
        returns (uint256 amountOut)
    {
        uint256 amountInWithFee = amountIn * fee;
        uint256 numerator = amountInWithFee * reserveOut;
        uint256 denominator = (reserveIn * 100000) + amountInWithFee;
        amountOut = numerator / denominator;
    }

    function getPair(address addr) internal returns (PairDataLocal memory data) {
        if (pairDatas[addr].token0 == address(0)) {
            //写入缓存
            data.token0 = IPair(addr).token0();
            data.token1 = IPair(addr).token1();
            pairDatas[addr] = data;
        } else {
            data.token0 = pairDatas[addr].token0;
            data.token1 = pairDatas[addr].token1;
        }
        (data.r0, data.r1) = IPairV1(addr).getReserves();
    }
    //gas save

    function getDecimals(address addr) internal returns (uint256 d) {
        if (tokenDecimals[addr] == 0) {
            d = 10 ** IERC20(addr).decimals();
            tokenDecimals[addr] = d;
        } else {
            d = tokenDecimals[addr];
        }
    }
    //兼容v1 v2
    //function getReserves(address addr) internal view returns (uint112 r0, uint112 r1) {
    //    (r0, r1) = IPairV1(addr).getReserves();
    //}

    function pairDatasToFull(PairData[] memory input) public returns (PairDataFull[] memory d) {
        d = new PairDataFull[](input.length);
        for (uint256 i = 0; i < input.length; i++) {
            PairDataLocal memory p = getPair(input[i].addr);
            //目前只有v2stable需要使用d0 d1计算
            (uint256 d0, uint256 d1) =
                input[i].version == 22 ? (getDecimals(p.token0), getDecimals(p.token1)) : (uint256(0), uint256(0));
            d[i] = PairDataFull(
                input[i].addr, input[i].version, input[i].fee, input[i].fp, p.token0, p.token1, p.r0, p.r1, d0, d1
            );
        }
    }
    /**
     * util end **************
     */

    /**
     * bull begin **************
     */
    /*
    function getAmountsOutByPairOnline(address tokenIn, uint amountIn, PairData[] memory pairs) public view returns (uint[] memory amounts, uint amountOut, address tokenOut) {
        uint fee;
        amounts = new uint[](pairs.length+1);
        amounts[0] = amountIn;
        for (uint i = 0; i < pairs.length; i++) {
            if(i > 0) tokenIn = tokenOut;
            if(amounts[i] == 0){
                amounts[i + 1] = 0;
            } else {
                (tokenOut, fee) = tokenIn == IPair(pairs[i].addr).token0() ? (IPair(pairs[i].addr).token1(), pairs[i].fee0) : (IPair(pairs[i].addr).token0(), pairs[i].fee1);
                (uint reserve0, uint reserve1) = IPairV1(pairs[i].addr).getReserves();
                (uint reserveIn, uint reserveOut) = tokenIn == IPair(pairs[i].addr).token0() ? (reserve0, reserve1) : (reserve1, reserve0);
                if(pairs[i].version == 22){
                    amounts[i + 1] = getAmountOutWithFeeStable(amounts[i], reserveIn, reserveOut, 10**IERC20(tokenIn).decimals(), 10**IERC20(tokenOut).decimals(), fee);
                } else {
                    amounts[i + 1] = getAmountOutWithFee(amounts[i], reserveIn, reserveOut, fee);
                }
            }
        }
        amountOut = amounts[amounts.length - 1];
    }
    */
    //查询使用单条路径还是多条路径
    function getAmountsExSmart(
        address _tokenIn,
        uint256 _sumIn,
        uint256[] memory _amountsIn,
        PairDataFull[][] memory _pairs
    ) internal view returns (uint256[][] memory amountsSmart, uint256 sumOut, bool isMulti, address tokenOut) {
        (uint256[] memory singleOuts,, address _tokenOut) = getAmountOutsByPairsFull(_tokenIn, _sumIn, _pairs[0]);
        tokenOut = _tokenOut;
        if (_amountsIn.length == 1) {
            amountsSmart = new uint256[][](1);
            amountsSmart[0] = singleOuts;
            sumOut = singleOuts[singleOuts.length - 1];
            isMulti = false;
        } else {
            uint256[][] memory amountsMulti = new uint[][](_pairs.length);
            uint256 sum3;
            for (uint256 i; i < _amountsIn.length; i++) {
                (uint256[] memory amounts,,) = getAmountOutsByPairsFull(_tokenIn, _amountsIn[i], _pairs[i]);

                amountsMulti[i] = amounts;
                sum3 += amounts[amounts.length - 1];
            }

            if (sum3 > singleOuts[singleOuts.length - 1]) {
                amountsSmart = new uint256[][](amountsMulti.length);
                amountsSmart = amountsMulti;
                sumOut = sum3;
                isMulti = true;
            } else {
                amountsSmart = new uint256[][](1);
                amountsSmart[0] = singleOuts;
                sumOut = singleOuts[singleOuts.length - 1];
                isMulti = false;
            }
        }
    }
    //查询用 todo:改成bytes[] calldata pumps提升签名速度
    /*
    function getAmountsExSmartOnline(address _tokenIn, uint _sumIn, uint256[] memory _amountsIn, PairData[][] memory _pairs) external view returns (uint256[][] memory amountsSmart, uint sumOut, bool isMulti, address tokenOut) {
        (uint256[] memory singleOuts, ,address _tokenOut) = getAmountsOutByPairOnline(_tokenIn, _sumIn, _pairs[0]);
        tokenOut = _tokenOut;
        if(_amountsIn.length == 1){
            amountsSmart = new uint256[][](1);
            amountsSmart[0] = singleOuts;
            sumOut = singleOuts[singleOuts.length-1];
            isMulti = false;
        } else {
            uint[][] memory amountsMulti = new uint[][](_pairs.length);
            uint sum3;
            for(uint i; i< _amountsIn.length;i++){
                (uint[] memory amounts,,) = getAmountsOutByPairOnline(_tokenIn, _amountsIn[i], _pairs[i]);

                amountsMulti[i] = amounts;
                sum3 += amounts[amounts.length-1];
            }

            if(sum3 > singleOuts[singleOuts.length-1]){
                amountsSmart = new uint256[][](amountsMulti.length);
                amountsSmart = amountsMulti;
                sumOut = sum3;
                isMulti = true;
            } else {
                amountsSmart = new uint256[][](1);
                amountsSmart[0] = singleOuts;
                sumOut = singleOuts[singleOuts.length-1];
                isMulti = false;
            }
        }
    }
    */

    function swapExWithWhaleSmart(
        address tokenIn,
        uint256 sumIn,
        uint256 minOut,
        uint256[] memory amountsIn,
        bytes[] calldata pairsRaw,
        Whale calldata whale
    ) external onlyOperator {
        require(verifyWhale(whale), "ERROR W");
        uint256 b = getBalance(tokenIn);
        require(b > 0, "ERROR 0");

        //初始化pairs
        PairDataFull[][] memory pairs = new PairDataFull[][](amountsIn.length);
        //disable fullmatch
        unchecked {
            if (b < sumIn) {
                uint256 newSum;
                if (minOut > 0) minOut = minOut * b / sumIn;
                for (uint256 i; i < amountsIn.length; i++) {
                    if (amountsIn[i] > 0) {
                        amountsIn[i] = amountsIn[i] * b / sumIn;
                        newSum = newSum + amountsIn[i];
                    }
                    (PairDataFull[] memory p,) = _getMevFull(pairsRaw[i]);
                    pairs[i] = p;
                }
                if (newSum != b) amountsIn[0] = amountsIn[0] - (newSum - b); //避免出现残留
                sumIn = b;
            } else {
                for (uint256 i; i < amountsIn.length; i++) {
                    (PairDataFull[] memory p,) = _getMevFull(pairsRaw[i]);
                    pairs[i] = p;
                }
            }
        }
        (uint256[][] memory amountsSmart, uint256 sumOut,, address tokenOut) =
            getAmountsExSmart(tokenIn, sumIn, amountsIn, pairs);

        require(sumOut >= minOut, "ERROR 2");

        for (uint256 i; i < amountsSmart.length; i++) {
            _swapFull(tokenIn, amountsSmart[i], pairs[i]);
        }
        require(IERC20(tokenOut).balanceOf(address(this)) >= minOut, "ERROR 3");
    }
    /**
     * bull end **************
     */

    /**
     *  pump begin **************
     */
    function _getMevFull(bytes memory datas) internal returns (PairDataFull[] memory d, uint256 mevType) {
        uint256 length = (datas.length / 27);
        d = new PairDataFull[](length);
        for (uint256 i = 0; i < length; i++) {
            address addr;
            uint8 version;
            uint24 fee;
            uint24 fp;
            assembly {
                version := mload(add(datas, add(1, mul(i, 27))))
                fee := mload(add(datas, add(4, mul(i, 27))))
                fp := mload(add(datas, add(7, mul(i, 27))))
                addr := mload(add(datas, add(27, mul(i, 27))))
            }
            PairDataLocal memory p = getPair(addr);
            //struct PairDataFull {  address addr; uint version; uint fee0; uint fee1; address token0; address token1; uint r0; uint r1; uint d0; uint d1;}
            (uint256 d0, uint256 d1) =
                version == 22 ? (getDecimals(p.token0), getDecimals(p.token1)) : (uint256(0), uint256(0));
            d[i] = PairDataFull(addr, version, fee, fp, p.token0, p.token1, p.r0, p.r1, d0, d1);

            if (mevType == 0 && (version != 2 && version != 1)) mevType = 1; //1是需要使用findMaxGolden
        }
    }

    function _decodePathBytes(bytes memory datas)
        public
        returns (address tokenIn, uint256 cost, uint256 mevType, PairDataFull[] memory d)
    {}

    function _swapFull(address input, uint256[] memory amounts, PairDataFull[] memory p)
        public
        onlyOperator
        returns (address output)
    {
        if (p[0].version != 21) IERC20(input).transfer(p[0].addr, amounts[0]);

        //uint fee;
        uint256 amountOutSafe;
        uint256 amountIn;
        for (uint256 i; i < p.length; i++) {
            if (i > 0) input = output;
            (address token0, address token1) = (p[i].token0, p[i].token1);
            //(output, fee) = input == token0 ? (token1, pairs[i].fee0) : (token0, pairs[i].fee1);
            output = input == token0 ? token1 : token0;

            uint256 amountOut = amounts[i + 1];
            if (amountOut == 0) continue;
            (uint256 amount0Out, uint256 amount1Out) =
                input == token0 ? (uint256(0), amountOut) : (amountOut, uint256(0));
            //address to = i < p.length - 1 ? p[i+1].addr : address(this);
            address to = (i >= p.length - 1 || p[i + 1].version == 21) ? address(this) : p[i + 1].addr;

            amountIn = i == 0 ? amounts[0] : amountOutSafe;
            if (i < p.length - 1 && p[i + 1].version == 21) {
                amountOutSafe = IERC20(output).balanceOf(address(this)); //下个pair是onlyrouter，先存放当前的token数量
            }

            if (p[i].version == 1) {
                IPairV1(p[i].addr).swap(amount0Out, amount1Out, to);
            } else if (p[i].version == 2 || p[i].version == 22) {
                IPair(p[i].addr).swap(amount0Out, amount1Out, to, new bytes(0));
            } else if (p[i].version == 21) {
                //use router
                address[] memory path = new address[](2);
                path[0] = input;
                path[1] = output;
                if (IERC20(input).allowance(address(this), uniSwapRouter) < amountIn) {
                    IERC20(input).approve(uniSwapRouter, amountIn);
                }
                IRouter(uniSwapRouter).swapExactTokensForTokensSupportingFeeOnTransferTokens(
                    amountIn, amountOut, path, to, block.timestamp + 100
                );
            } else {
                revert("error version");
            }
            if (i < p.length - 1 && p[i + 1].version == 21) {
                amountOutSafe = IERC20(output).balanceOf(address(this)) - amountOutSafe;
            } //交易完成后计算差值
        }
    }

    struct SwapFullSafeData {
        uint256 amountInBefore; //计算到账
        uint256 amountInAfter; //实际到账
        uint256 amountOut;
        uint256 botReserve;
        uint256 dIn;
        uint256 dOut;
        uint112 rIn;
        uint112 rOut;
    }

    function _swapFullSafe(address tokenIn, uint256 amountIn, PairDataFull[] memory p)
        public
        onlyOperator
        returns (address tokenOut, uint256[] memory fees)
    {
        fees = new uint256[](p.length);
        SwapFullSafeData memory t;

        if (p[0].version != 21) IERC20(tokenIn).transfer(p[0].addr, amountIn);

        t.amountInBefore = amountIn;
        t.amountInAfter = amountIn;
        for (uint256 i = 0; i < p.length; i++) {
            //有些fee token转账时候会变动pair的r，需要动态获取
            (t.rIn, t.rOut) = IPairV1(p[i].addr).getReserves();
            (t.rIn, t.rOut) = tokenIn == p[i].token0 ? (t.rIn, t.rOut) : (t.rOut, t.rIn);
            tokenOut = tokenIn == p[i].token0 ? p[i].token1 : p[i].token0;

            if (p[i].version == 21) {
                if (i > 0) t.amountInAfter = IERC20(tokenIn).balanceOf(address(this)) - t.botReserve;
            } else {
                //不是onlyRouter
                t.amountInAfter = IERC20(tokenIn).balanceOf(address(p[i].addr)) - t.rIn; //实际到账
            }

            //下一个pair是only router, swap前记录amountOut给下一次循环使用
            if (i < p.length - 1 && p[i + 1].version == 21) t.botReserve = IERC20(tokenOut).balanceOf(address(this));

            //calc fee
            if (t.amountInBefore == t.amountInAfter) {
                fees[i] = 0;
            } else {
                if (t.amountInBefore < t.amountInAfter) {
                    //honey pot或者计算错误, 下一个pool获得的token比预期的多
                    fees[i] = 0;
                } else {
                    fees[i] = (t.amountInBefore - t.amountInAfter) * 100000 / t.amountInBefore;
                    if (fees[i] > 1 && fees[i] % 10 != 0) fees[i] = fees[i] + 1;
                }
            }

            if (p[i].version == 22) {
                //stableV2
                //(t.dIn, t.dOut) = tokenIn == p[i].token0 ? (p[i].d0, p[i].d1) : (p[i].d1, p[i].d0);
                //t.amountOut = getAmountOutWithFeeStable(t.amountInAfter, t.rIn, t.rOut, t.dIn, t.dOut, p[i].fee0); //只计算router fee
                t.amountOut = IPair(p[i].addr).getAmountOut(t.amountInAfter, tokenIn);
            } else {
                t.amountOut = getAmountOutWithFee(t.amountInAfter, t.rIn, t.rOut, p[i].fp); //只计算router fee
            }

            (uint256 amount0Out, uint256 amount1Out) =
                tokenIn == p[i].token0 ? (uint256(0), t.amountOut) : (t.amountOut, uint256(0));

            //最后一个pair发送给自己
            address to = (i == p.length - 1 || p[i + 1].version == 21) ? address(this) : p[i + 1].addr;

            if (p[i].version == 1) {
                IPairV1(p[i].addr).swap(amount0Out, amount1Out, to);
            } else if (p[i].version == 2 || p[i].version == 22) {
                IPair(p[i].addr).swap(amount0Out, amount1Out, to, new bytes(0));
            } else if (p[i].version == 21) {
                //only Router
                address[] memory path = new address[](2);
                path[0] = tokenIn;
                path[1] = tokenOut;

                //检查router的授权额度
                if (IERC20(tokenIn).allowance(address(this), uniSwapRouter) < t.amountInAfter) {
                    IERC20(tokenIn).approve(uniSwapRouter, t.amountInAfter);
                }

                IRouter(uniSwapRouter).swapExactTokensForTokensSupportingFeeOnTransferTokens(
                    t.amountInAfter, 0, path, to, block.timestamp + 100
                );
            } else {
                revert("error version");
            }
            t.amountInBefore = t.amountOut; //把取出的token数量当做下一次循环的amountInBefore
            tokenIn = tokenOut;
        }
    }

    function findMax(address tokenIn, PairDataFull[] memory p) public pure returns (uint256) {
        uint256 Ea;
        uint256 Eb;
        uint256 ra;
        uint256 rb;
        uint256 rb1;
        uint256 rc;
        uint256 Efee;
        uint256 fee;
        address tokenOut;

        PairDataFull memory d = p[0];

        if (p.length == 1) {
            if (d.token0 == tokenIn) {
                Ea = d.r0;
                Eb = d.r1;
                tokenOut = d.token1;
            } else {
                Ea = d.r1;
                Eb = d.r0;
                tokenOut = d.token0;
            }
            Efee = d.fp - d.fee;
            unchecked {
                return (sqrt(Ea * Eb * Efee / 100000) - Ea) * 100000 / Efee;
            }
        }

        if (d.token0 == tokenIn) {
            ra = d.r0;
            rb = d.r1;
            tokenOut = d.token1;
        } else {
            ra = d.r1;
            rb = d.r0;
            tokenOut = d.token0;
        }
        Efee = d.fp - d.fee;

        d = p[1];
        if (d.token0 == tokenOut) {
            rb1 = d.r0;
            rc = d.r1;
            tokenOut = d.token1;
        } else {
            rb1 = d.r1;
            rc = d.r0;
            tokenOut = d.token0;
        }
        fee = d.fp - d.fee;
        //disable fullmath
        unchecked {
            Ea = ra * rb1 / (rb1 + rb * fee / 100000);
            Eb = rb * rc * fee / 100000 / (rb1 + rb * fee / 100000); //可能溢出

            for (uint256 i = 2; i < p.length; i++) {
                ra = Ea;
                rb = Eb;
                d = p[i];
                if (d.token0 == tokenOut) {
                    rb1 = d.r0;
                    rc = d.r1;
                    tokenOut = d.token1;
                } else {
                    rb1 = d.r1;
                    rc = d.r0;
                    tokenOut = d.token0;
                }
                fee = d.fp - d.fee;
                Ea = ra * rb1 / (rb1 + rb * fee / 100000);
                Eb = rb * rc * fee / 100000 / (rb1 + rb * fee / 100000);
            }
            return (sqrt(Ea * Eb * Efee / 100000) - Ea) * 100000 / Efee;
        }
    }

    function findMaxGolden(address tokenIn, uint256 high, PairDataFull[] memory pairs)
        internal
        view
        returns (uint256)
    {
        uint256 low = goldenSectionCfg[tokenIn];
        if (low == 0) low = 1e16; //没有设置cfg，设置默认值避免计算出错
        uint256 tolerance = low * findMaxEps;
        uint8 step;
        uint8 stepMax = findMaxStep;

        (, uint256 amountOutx1,) = getAmountOutsByPairsFull(tokenIn, low, pairs);
        if (amountOutx1 < low) return 0;
        //uint phi = 1618034; // Approximation of 1e6 * ((1 + sqrt(5)) / 2) //1618034
        uint256 max = MAX_UINT112;
        uint256 x1 = high - ((high - low) * 1e6) / 1618034;
        uint256 x2 = low + ((high - low) * 1e6) / 1618034;

        (, amountOutx1,) = getAmountOutsByPairsFull(tokenIn, x1, pairs);
        (, uint256 amountOutx2,) = getAmountOutsByPairsFull(tokenIn, x2, pairs);

        uint256 f1 = amountOutx1 + max - x1; //确保不溢出
        uint256 f2 = amountOutx2 + max - x2; //确保不溢出

        while (high > tolerance + low || step < stepMax) {
            //超过32步就直接返回
            if (f1 > f2) {
                high = x2;
                x2 = x1;
                f2 = f1;
                x1 = high - ((high - low) * 1e6) / 1618034;
                (, amountOutx1,) = getAmountOutsByPairsFull(tokenIn, x1, pairs);
                f1 = amountOutx1 + max - x1; //确保不溢出
            } else if (f1 < f2) {
                low = x1;
                x1 = x2;
                f1 = f2;
                x2 = low + ((high - low) * 1e6) / 1618034;
                (, amountOutx2,) = getAmountOutsByPairsFull(tokenIn, x2, pairs);
                f2 = amountOutx2 + max - x2; //确保不溢出
            } else {
                low = x1;
                high = x2;
                x1 = high - ((high - low) * 1e6) / 1618034;
                x2 = low + ((high - low) * 1e6) / 1618034;
                (, amountOutx1,) = getAmountOutsByPairsFull(tokenIn, x1, pairs);
                (, amountOutx2,) = getAmountOutsByPairsFull(tokenIn, x2, pairs);
                f1 = amountOutx1 + max - x1; //确保不溢出
                f2 = amountOutx2 + max - x2; //确保不溢出
            }
            step++;
        }
        return (high + low) / 2;
    }

    function getAmountOutsByPairsFull(address tokenIn, uint256 amountIn, PairDataFull[] memory p)
        public
        view
        returns (uint256[] memory amounts, uint256 amountOut, address tokenOut)
    {
        uint256 fee;
        amounts = new uint256[](p.length+1);
        amounts[0] = amountIn;
        for (uint256 i = 0; i < p.length; i++) {
            if (i > 0) tokenIn = tokenOut;
            if (amounts[i] == 0) {
                amounts[i + 1] = 0;
            } else {
                tokenOut = tokenIn == p[i].token0 ? p[i].token1 : p[i].token0;
                fee = p[i].fp - p[i].fee;
                //(tokenOut, fee) = tokenIn == p[i].token0 ? (p[i].token1, p[i].fee) : (p[i].token0, p[i].fee);
                (address token0,) = sortTokens(tokenIn, tokenOut);
                (uint256 reserveIn, uint256 reserveOut) = tokenIn == token0 ? (p[i].r0, p[i].r1) : (p[i].r1, p[i].r0);
                if (p[i].version == 22) {
                    //(uint256 dIn, uint256 dOut) = tokenIn == token0 ? (p[i].d0, p[i].d1) : (p[i].d1, p[i].d0);
                    //amounts[i + 1] = getAmountOutWithFeeStable(amounts[i], reserveIn, reserveOut, dIn, dOut, fee);
                    amounts[i + 1] = IPair(p[i].addr).getAmountOut(amounts[i], tokenIn);
                } else {
                    amounts[i + 1] = getAmountOutWithFee(amounts[i], reserveIn, reserveOut, fee);
                }
            }
        }
        amountOut = amounts[amounts.length - 1];
    }
    //支持多个币之间搬砖
    /*
    function pumpSmart(address[] calldata tokenIns, bytes[] calldata pumps, uint256 cost)
        external
        onlyOperator
        returns (uint256 reward, uint256 length, uint256 done)
    {
        BalanceDesc memory banalce;
        //统计稳定币持仓
        for (uint256 i; i < tokenIns.length; i++) {
            if (i == 0) {
                banalce.maxIn = getBalance(tokenIns[0]); //init balance
                require(banalce.maxIn > 0, "ERROR EEE");
                banalce.sum += banalce.maxIn;
            } else {
                banalce.sum += getBalance(tokenIns[i]);
            }
        }
        for (uint256 i = 1; i < pumps.length + 1; i++) {
            //超过流动性的时候重复一次当前交易
            (PairDataFull[] memory pairs, uint256 mevType) = _getMevFull(pumps[i - 1]);
            uint256 amountIn = mevType == 1
                ? findMaxGolden(tokenIns[0], banalce.maxIn * 110 / 100, pairs)
                : findMax(tokenIns[0], pairs);
            if (amountIn > 0 && amountIn < MAX_UINT112) {
                if (amountIn > banalce.maxIn) {
                    amountIn = banalce.maxIn;
                    banalce.outOfbalance = true;
                    banalce.redo = true;
                } else {
                    banalce.redo = false;
                }
                (uint256[] memory amounts,,) = getAmountOutsByPairsFull(tokenIns[0], amountIn, pairs);
                if (amountIn + cost < amounts[amounts.length - 1]) {
                    _swapFull(tokenIns[0], amounts, pairs);
                    done += 1;
                    //超过了流动性, 产生收入，更新流动性最大值，每个路径跑完更新一次，避免maxIn == 0
                    //if(redo){
                    banalce.maxIn = getBalance(tokenIns[0]);
                    if (banalce.maxIn == 0) break;
                    //}
                }
            }
            if (gasleft() < 220000) break;
            //超过流动性，再跑一次
            if (banalce.redo) i = i - 1;
        }
        if (done > 0) {
            for (uint256 i; i < tokenIns.length; i++) {
                banalce.sumAfter += getBalance(tokenIns[i]);
            }
            require(banalce.sumAfter >= banalce.sum, "ERROR PO");
            length = pumps.length;
            if (banalce.outOfbalance) {
                done += 100; //超过流动性
                length += 100;
            }
            reward = banalce.sumAfter - banalce.sum;
            emit Reward(reward, length, done);
        }
    }
    */

    struct PumpData {
        uint8 convertEth; //自动变换eth
        uint8 tokenIn0or1;
        uint8 calc; //计算方式 0: dydx(只有v2) 1:golden(黄金分割法) 2:固定使用 amountIn
        uint24 gasLimit; //gas消耗
        uint88 cost;
        uint112 amountIn; //如果提供了amountIn则使用精确模式，
        PairData[] pairs;
    }

    function decodePacked(bytes memory packedData) public pure returns (PumpData memory) {
        // 首先解析前四个字节，获取 `cost`、`amountIn`、`convertEth` 和 `tokenIn0or1` 的值
        //return packedData[11:25];
        uint8 convertEth; //解析有问题，要替换成uint8
        uint8 tokenIn0or1;
        uint8 calc;
        uint24 gasLimit;
        uint88 cost;
        uint112 amountIn;
        assembly {
            /*
            cost := mload(add(packedData, 11))
            amountIn := mload(add(packedData, 25))
            convertEth := mload(add(packedData, 26))
            tokenIn0or1 := mload(add(packedData, 27))
            calc := mload(add(packedData, 28))
            gasLimit := mload(add(packedData, 31))
            */
            convertEth := mload(add(packedData, 1))
            tokenIn0or1 := mload(add(packedData, 2))
            calc := mload(add(packedData, 3))
            gasLimit := mload(add(packedData, 6))
            cost := mload(add(packedData, 17))
            amountIn := mload(add(packedData, 31))
        }

        uint256 pairsLength = (packedData.length - 31) / 26; //每个pair的长度是26,前面4个对象的长度是27
        PairData[] memory pairs = new PairData[](pairsLength);
        for (uint256 i; i < pairsLength; i++) {
            uint8 version;
            uint16 fee;
            uint24 fp;
            address addr;
            assembly {
                version := mload(add(packedData, add(32, mul(i, 26)))) //1+27
                fee := mload(add(packedData, add(34, mul(i, 26)))) //2+28
                fp := mload(add(packedData, add(37, mul(i, 26)))) //3+30
                addr := mload(add(packedData, add(57, mul(i, 26)))) //20+33
            }
            pairs[i] = PairData({addr: addr, version: version, fee: fee, fp: fp});
        }

        // 返回解码后的 PumpData 对象
        return PumpData({
            convertEth: convertEth,
            tokenIn0or1: tokenIn0or1,
            calc: calc,
            gasLimit: gasLimit,
            cost: cost,
            amountIn: amountIn,
            pairs: pairs
        });
    }

    event PumpResults(uint8[], uint112[]);

    enum PumpResult {
        None,
        Success, //1.成功
        SuccessOverlow, //2.成功但是超出流动性
        FindMax0, //3:findmax没有利润
        Balance0, //4:没有钱
        SwapErr, //5:swap出错
        FindMaxErr, //6:计算出有利润，但是实际没有
        RewardTooLow //7:有利润但是不能覆盖gas损失
    }

    function _pump(PumpData calldata pump) public returns (PumpResult status, uint256 reward) {
        PairDataFull[] memory pairs = pairDatasToFull(pump.pairs);
        address tokenIn;
        uint256 amountIn;
        uint256 balance;
        bool outOfBalance;

        if (pump.pairs.length == 0) return (PumpResult.None, 0);

        tokenIn = pump.tokenIn0or1 == 0 ? pairs[0].token0 : pairs[0].token1;

        if (pump.amountIn > 0) {
            amountIn = pump.amountIn;
        } else {
            if (pump.calc == 0) {
                amountIn = findMax(tokenIn, pairs);
            } else {
                balance = IERC20(tokenIn).balanceOf(address(this));
                if (balance == 0) {
                    return (PumpResult.Balance0, 0);
                }
                amountIn = findMaxGolden(tokenIn, balance * 110 / 100, pairs);
            }
        }

        if (amountIn == 0 || amountIn >= MAX_UINT112) {
            return (PumpResult.FindMax0, 0);
        }

        if (balance == 0) {
            //还未赋值
            balance = IERC20(tokenIn).balanceOf(address(this));
            if (balance == 0) return (PumpResult.Balance0, 0);
        }

        if (amountIn > balance) {
            amountIn = balance;
            outOfBalance = true;
        }

        (uint256[] memory amounts,, address tokenOut) = getAmountOutsByPairsFull(tokenIn, amountIn, pairs);
        if (amountIn >= amounts[amounts.length - 1]) {
            status = PumpResult.FindMaxErr; //没有利润
        } else if (amountIn + pump.cost < amounts[amounts.length - 1]) {
            if (tokenIn != tokenOut) balance = IERC20(tokenOut).balanceOf(address(this)) + amountIn;

            _swapFullSafe(tokenIn, amountIn, pairs); //todo: 需要计算异常
            uint256 balanceAfter = IERC20(tokenOut).balanceOf(address(this));

            if (balanceAfter > balance) {
                reward = balanceAfter - balance;
            } else {
                //计算结果与实际结果不符合，通常是fee错误
                revert("3");
            }

            if (pump.convertEth == 1) {
                IERC20(tokenOut).withdraw(reward + amountIn); //转成eth
                IERC20(tokenIn).deposit{value: address(this).balance}();
            }
            status = outOfBalance ? PumpResult.SuccessOverlow : PumpResult.Success;
        } else {
            //利润小于cost
            status = PumpResult.RewardTooLow;
        }
    }

    function pumpWrapper(PumpData[] memory pumps) internal returns (uint8[] memory status, uint112[] memory rewards) {
        status = new uint8[](pumps.length);
        rewards = new uint112[](pumps.length);

        for (uint256 i = 1; i < pumps.length + 1; i++) {
            PumpData memory pump = pumps[i - 1];
            //不够gas处理后面的逻辑
            if (gasleft() < pump.gasLimit + 20000) break;

            try this._pump(pump) returns (PumpResult _status, uint256 _reward) {
                if (status[i - 1] == 0) status[i - 1] = uint8(_status);
                rewards[i - 1] += uint112(_reward);

                //超出流动性，判断剩余gas是否够用，再来一次
                if (_status == PumpResult.SuccessOverlow) {
                    i = i - 1;
                }
            } catch {
                status[i - 1] = uint8(PumpResult.SwapErr);
            }
        }
        emit PumpResults(status, rewards); //10条results大概7500 gas
    }

    function pumpSmart(bytes[] calldata raws)
        public
        onlyOperator
        returns (uint8[] memory status, uint112[] memory rewards)
    {
        PumpData[] memory pumps = new PumpData[](raws.length);
        for (uint256 i = 1; i < raws.length + 1; i++) {
            pumps[i - 1] = decodePacked(raws[i - 1]);
        }

        return pumpWrapper(pumps);
    }

    function junkSwap(bytes[] calldata raws)
        public
        onlyOperator
        returns (uint8[] memory status, uint112[] memory rewards)
    {
        PumpData[] memory pumps = new PumpData[](raws.length);

        for (uint256 i = 1; i < raws.length + 1; i++) {
            PumpData memory pump = decodePacked(raws[i - 1]);
            bool recentlyUpdate = false;
            for (uint256 j = 0; j < pump.pairs.length; j++) {
                if (pump.pairs[i].version != 2) {
                    recentlyUpdate = true;
                    break;
                } else {
                    (,, uint32 updateTime) = IPair(pump.pairs[i].addr).getReserves();
                    if (block.timestamp - updateTime < 3) {
                        recentlyUpdate = true;
                        break;
                    }
                }
                if (!recentlyUpdate) {
                    pump.pairs = new PairData[](0);
                }
                pumps[i - 1] = pump;
            }
        }

        return pumpWrapper(pumps);
    }

    /**
     * pump end **************
     */

    /**
     *  test begin 辅助功能 **************
     */

    struct CheckPairFeeInputDesc {
        PairData[] pair;
        PairData[] prePair;
        address tokenIn;
    }

    struct CheckPairFeeDesc {
        uint256 r;
        uint256 rAfter;
        uint256 r0;
        uint256 r1;
        uint256 fee;
        address addr;
    }

    struct CheckPairFeeResultDesc {
        uint256 fee0;
        uint256 fee1;
    }

    function batchCheckPairFee(CheckPairFeeInputDesc[] calldata input)
        public
        returns (CheckPairFeeResultDesc[] memory)
    {
        CheckPairFeeResultDesc[] memory fees = new CheckPairFeeResultDesc[](input.length);
        for (uint256 z; z < input.length; z++) {
            address tokenIn = input[z].tokenIn;
            CheckPairFeeDesc memory t;
            CheckPairFeeResultDesc memory fee;
            t.addr = input[z].pair[0].addr;
            //购买目标token
            uint256 amountIn = IERC20(tokenIn).balanceOf(address(this));
            require(amountIn > 0, "NotEnoughtStable");

            //购买目标token
            (PairDataFull[] memory pairs) = pairDatasToFull(input[z].prePair);
            //没有合适的stable，需要前置swap
            if (input[z].prePair.length > 0) {
                //设置routerFee = 10% ，保证安全持有两种货币
                //pairs[0].fee0 = 90000;
                //pairs[0].fee1 = 90000;
                //需要把tokenIn货币转换成_pair的某一个token
                try this._swapFullSafe(tokenIn, amountIn / 2, pairs) returns (address _tokenIn, uint256[] memory) {
                    tokenIn = _tokenIn;
                } catch {
                    //_prePair的fee不对
                    fee.fee0 = 900001;
                    fee.fee1 = 900001;
                    fees[z] = fee;
                    continue;
                }
                amountIn = IERC20(tokenIn).balanceOf(address(this));
                require(amountIn > 0, "BadFee"); //现在持有的stable
            }

            (pairs) = pairDatasToFull(input[z].pair);
            //设置routerFee = 10% ，保证安全持有两种货币
            //pairs[0].fee0 = 90000;
            //pairs[0].fee1 = 90000;
            //把tokenIn分一半
            try this._swapFullSafe(tokenIn, amountIn / 2, pairs) returns (address _tokenIn, uint256[] memory) {
                if (IERC20(_tokenIn).balanceOf(address(this)) == 0) {
                    //swap出来的另外一个代币不存在自己的账号上
                    fee.fee0 = 900003;
                    fee.fee1 = 900003;
                    fees[z] = fee;
                    continue;
                }
            } catch {
                fee.fee0 = 900002; //router的fee不对, 或者空pair
                fee.fee1 = 900002;
                fees[z] = fee;
                continue;
            }

            //测试对pair的转账
            for (uint256 i = 0; i < 2; i++) {
                tokenIn = i == 0 ? pairs[0].token0 : pairs[0].token1;
                amountIn = getBalance(tokenIn) / 2;
                //uint256 total = IERC20(tokenIn).totalSupply();
                try IERC20(tokenIn).transfer(t.addr, amountIn) {}
                catch {
                    //转账到Pair失败，不能卖
                    i == 0 ? fee.fee0 = 900000 : fee.fee1 = 900000;
                    continue;
                }
                (t.r0, t.r1) = IPairV1(t.addr).getReserves();
                t.r = i == 0 ? t.r0 : t.r1;
                t.rAfter = IERC20(tokenIn).balanceOf(t.addr);
                /*
                if (IERC20(tokenIn).totalSupply() - total > 0) {
                    //转账后总货币增发了，honey token
                    i == 0 ? fee0 = 888888 : fee1 = 888888;
                    continue;
                }
                */
                if (t.rAfter < t.r) {
                    //通缩异常
                    i == 0 ? fee.fee0 = 800000 : fee.fee1 = 800000;
                } else if (t.rAfter == t.r) {
                    //100% fee
                    i == 0 ? fee.fee0 = 100000 : fee.fee1 = 100000;
                } else {
                    if ((t.rAfter - t.r) > amountIn) {
                        //pair获得的token比转入的还多, honey token
                        i == 0 ? fee.fee0 = 666666 : fee.fee1 = 666666;
                    } else {
                        t.fee = (amountIn - (t.rAfter - t.r)) * 100000 / amountIn;
                        if (t.fee > 1 && t.fee % 10 != 0) t.fee = t.fee + 1;
                        i == 0 ? fee.fee0 = t.fee : fee.fee1 = t.fee;
                    }
                }
            }
            fees[z] = fee;
        }
        return fees;
    }

    struct CheckMevsInputDesc {
        bool tokenIn0or1;
        PairData[] pairs;
    }

    struct CheckMevsResultDesc {
        uint256 gas; //正反的和取最大值
        uint256[] fee0; //正向gas, 如果是空数组则不可交易
        uint256[] fee1; //反向gas, 如果是空数组则不可交易
    }

    function batchCheckMev(CheckMevsInputDesc[] calldata d) public returns (CheckMevsResultDesc[] memory results) {
        results = new CheckMevsResultDesc[](d.length);
        uint256 gasLeft;
        for (uint256 i; i < d.length; i++) {
            //result0, result1, gas0, gas1
            CheckMevsResultDesc memory result;
            //计算第一次的getPair gas
            //gasLeft = gasleft();
            //result.gasGetPair = gasLeft - gasleft();

            //1.正向操作
            PairDataFull[] memory pairs = pairDatasToFull(d[i].pairs); //先缓存pair信息，避免写入合约的gas消耗影响mev的结果

            gasLeft = gasleft();
            pairs = pairDatasToFull(d[i].pairs);
            address tokenIn = d[i].tokenIn0or1 ? pairs[0].token0 : pairs[0].token1;
            //每次取一半token测试，避免单向mev耗尽货币
            try this._swapFullSafe(tokenIn, getBalance(tokenIn) / 2, pairs) returns (address, uint256[] memory _fee) {
                result.fee0 = _fee;
            } catch {
                //正向交易失败了 fee的长度是0
            }
            result.gas = gasLeft - gasleft(); //第一次swap的gas最大

            //计算右边的的tokenIn
            address tokenOut;
            for (uint256 j = 0; j < pairs.length; j++) {
                tokenOut = pairs[j].token0 == tokenIn ? pairs[j].token1 : pairs[j].token0;
                tokenIn = tokenOut;
            }

            //check resvers 翻转pair
            uint256 pairLen = d[i].pairs.length;
            PairData[] memory pairsR = new PairData[](pairLen);
            for (uint256 j = pairLen; j > 0; j--) {
                pairsR[pairLen - j] = d[i].pairs[j - 1];
            }
            //1.反操作
            pairs = pairDatasToFull(pairsR);

            try this._swapFullSafe(tokenIn, getBalance(tokenIn) / 2, pairs) returns (address, uint256[] memory fee) {
                result.fee1 = fee;
            } catch {
                //逆向交易失败了, fee的长度是0
            }
            results[i] = result;
        }
        //return results;
    }

    struct GetPairInfoResultDesc {
        uint256 id;
        string s0;
        string s1;
        address addr;
        address token0;
        address token1;
        uint256 r0;
        uint256 r1;
        uint256 d0;
        uint256 d1;
        uint256 lastUpdate;
        bool stable;
    }
    //批量更新pair的reserves

    function _getPairBaseInfo(address addr)
        public
        view
        returns (address token0, address token1, string memory s0, string memory s1, uint256 d0, uint256 d1)
    {
        token0 = IPair(addr).token0();
        token1 = IPair(addr).token1();
        s0 = IERC20(token0).symbol();
        s1 = IERC20(token1).symbol();
        d0 = IERC20(token0).decimals();
        d1 = IERC20(token1).decimals();
    }

    function batchGetPairInfo(address[] memory addrs) public view returns (GetPairInfoResultDesc[] memory) {
        GetPairInfoResultDesc[] memory ps = new GetPairInfoResultDesc[](addrs.length);
        bool checkStable;
        //is a v2Stable pairs
        try IPair(addrs[0]).stable() returns (bool) {
            checkStable = true;
        } catch {
            checkStable = false;
        }
        for (uint256 i; i < addrs.length; i++) {
            address addr = addrs[i];
            try this._getPairBaseInfo(addr) returns (
                address _token0, address _token1, string memory _s0, string memory _s1, uint256 _d0, uint256 _d1
            ) {
                GetPairInfoResultDesc memory p = GetPairInfoResultDesc({
                    addr: addr,
                    token0: _token0,
                    token1: _token1,
                    s0: _s0,
                    s1: _s1,
                    r0: 0,
                    r1: 0,
                    d0: _d0,
                    d1: _d1,
                    lastUpdate: 0,
                    id: 0,
                    stable: checkStable ? IPair(addr).stable() : false
                });
                (bool success, bytes memory data) = addr.staticcall(abi.encodeWithSignature("getReserves()"));
                require(success, "getReserves failed");

                if (data.length == 64) {
                    // Uniswap V1: returns 2 parameters
                    (p.r0, p.r1) = abi.decode(data, (uint112, uint112));
                } else if (data.length == 96) {
                    // Uniswap V2: returns 3 parameters
                    (p.r0, p.r1, p.lastUpdate) = abi.decode(data, (uint112, uint112, uint32));
                } else {
                    revert("Unexpected data length");
                }
                /*
                try IPair(addr).getReserves() returns (uint112 _r0, uint112 _r1, uint32 _t) {
                    p.r0 = _r0;
                    p.r1 = _r1;
                    //p.lastUpdate = _t;
                } catch {
                    try IPairV1(addr).getReserves() returns (uint112 _r0, uint112 _r1) {
                        p.r0 = _r0;
                        p.r1 = _r1;
                    } catch {
                        revert("e1");
                    }
                }
                */
                ps[i] = p;
            } catch {
                revert("e2");
            }
        }
        return ps;
    }
    //批量更新router的pair

    function batchGetRouterPairs(address router, uint256 from, uint256 to)
        public
        view
        returns (GetPairInfoResultDesc[] memory)
    {
        to = to + 1;

        try IRouter(router).factoryV2() returns (address _addr) {
            router = _addr;
        } catch {
            router = IRouter(router).factory();
        }

        uint256 allPairsLength;
        try IFactory(router).allPairsLength() returns (uint256 _length) {
            allPairsLength = _length;
        } catch {
            try IFactory(router).totalPairs() returns (uint256 _length) {
                allPairsLength = _length;
            } catch {
                revert("error pairs length");
            }
        }
        if (from >= allPairsLength) {
            return new GetPairInfoResultDesc[](0);
        }

        if (to >= allPairsLength) to = allPairsLength;

        address[] memory addrs = new address[](to-from);

        uint256 j;
        for (uint256 i = from; i < to; i++) {
            address addr = IFactory(router).allPairs(i);
            addrs[j] = addr;
            j++;
        }
        GetPairInfoResultDesc[] memory results = batchGetPairInfo(addrs);

        j = 0;
        for (uint256 i = from; i < to; i++) {
            results[j].id = i;
            j++;
        }
        return results;
    }
    /**
     *  test end **************
     */

    function _delegate(address implementation) public payable onlyOperator {
        require(implementation != address(0), "error logic address");
        assembly {
            calldatacopy(0, 0, calldatasize())
            let result := delegatecall(gas(), implementation, 0, calldatasize(), 0, 0)
            returndatacopy(0, 0, returndatasize())

            switch result
            case 0 { revert(0, returndatasize()) }
            default { return(0, returndatasize()) }
        }
    }

    //function destroyEx() public onlyOwner { selfdestruct(payable(msg.sender)); }

    receive() external payable {}

    fallback() external payable {
        _delegate(logicContact);
    }
}
