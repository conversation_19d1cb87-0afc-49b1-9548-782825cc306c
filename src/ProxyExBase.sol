// SPDX-License-Identifier: UNLICENSED

//import '@uniswap/lib/contracts/libraries/Babylonian.sol';
//import './FullMathV3.sol';

import "@openzeppelin/contracts/access/Ownable.sol";
//import "@openzeppelin/contracts/utils/Address.sol";
//import "@openzeppelin/contracts/utils/math/SafeMath.sol";

pragma solidity ^0.8.0;

interface IPair {
    function token0() external view returns (address);
    function token1() external view returns (address);
    function getReserves() external view returns (uint112 reserve0, uint112 reserve1, uint32 blockTimestampLast);
    function swap(uint256 amount0Out, uint256 amount1Out, address to, bytes calldata data) external;
}

pragma solidity ^0.8.0;

interface IPairV1 {
    function token0() external view returns (address);
    function token1() external view returns (address);
    function getReserves() external view returns (uint112 reserve0, uint112 reserve1);
    function swap(uint256 amount0Out, uint256 amount1Out, address to) external;
}

pragma solidity ^0.8.0;

interface IRouter {
    function swapExactTokensForTokens(
        uint256 amountIn,
        uint256 amountOutMin,
        address[] calldata path,
        address to,
        uint256 deadline
    ) external returns (uint256[] memory amounts);
    function swapExactTokensForTokensSupportingFeeOnTransferTokens(
        uint256 amountIn,
        uint256 amountOutMin,
        address[] calldata path,
        address to,
        uint256 deadline
    ) external;
}

pragma solidity ^0.8.0;

// File: @openzeppelin/contracts/token/ERC20/IERC20.sol
interface IERC20 {
    function totalSupply() external view returns (uint256);
    function balanceOf(address account) external view returns (uint256);
    function transfer(address recipient, uint256 amount) external returns (bool);
    function allowance(address owner, address spender) external view returns (uint256);
    function approve(address spender, uint256 amount) external returns (bool);
    function transferFrom(address sender, address recipient, uint256 amount) external returns (bool);
    function decimals() external view returns (uint256);
    function withdraw(uint256 wad) external;
    function deposit() external;

    event Transfer(address indexed from, address indexed to, uint256 value);
    event Approval(address indexed owner, address indexed spender, uint256 value);
}

// File: @openzeppelin/contracts/access/Roles.sol
pragma solidity ^0.8.0;

library Roles {
    struct Role {
        mapping(address => bool) bearer;
    }

    function add(Role storage role, address account) internal {
        require(!has(role, account), "Roles: account already has role");
        role.bearer[account] = true;
    }

    function remove(Role storage role, address account) internal {
        require(has(role, account), "Roles: account does not have role");
        role.bearer[account] = false;
    }

    function has(Role storage role, address account) internal view returns (bool) {
        require(account != address(0), "Roles: account is the zero address");
        return role.bearer[account];
    }
}

pragma solidity ^0.8.0;
pragma experimental ABIEncoderV2;

contract ProxyExBase is Ownable {
    //using SafeMath for uint256;
    using Roles for Roles.Role;

    mapping(address => PairDataLocal) pairDatas;
    Roles.Role private _operators;

    address public logicContact;
    address public uniSwapRouter;

    mapping(address => uint256) tokenDecimals;
    mapping(address => uint256) goldenSectionCfg;

    address vault; //金库

    uint8 findMaxStep = 30;
    uint16 findMaxEps = 250;
    uint112 constant MAX_UINT112 = 5192296858534827628530496329220095;

    event Reward(uint256, uint256, uint256);
    event MayFail(uint256); //0:success 1:not enough output 2:whale fail

    struct FeedList {
        address addr;
        uint256 min;
        uint256 max;
        uint256 balance;
        uint256 feedAmount;
    }

    //check allowance
    struct Whale {
        address addr;
        address token;
        uint256 amount;
        bool isEth;
        bool isGreater;
        address spender;
        address spenderToken;
        uint256 spenderAmount;
    }

    struct SwapData {
        address tokenIn;
        uint256 sumIn;
        uint256[] amountsIn;
        address[][] pairs;
        uint256 minOut;
    }

    struct PairDataLocal {
        address token0;
        address token1;
        uint256 r0;
        uint256 r1;
    } //uint256 fee0; //uint256 fee1;

    struct PairData {
        address addr;
        uint256 version;
        uint256 fee0;
        uint256 fee1;
    }

    struct PairDataFull {
        address addr;
        uint256 version;
        uint256 fee0;
        uint256 fee1;
        address token0;
        address token1;
        uint256 r0;
        uint256 r1;
    } //PairData和PairDataLocal的组合

    struct BalanceDesc {
        uint256 maxIn;
        uint256 sum;
        uint256 sumAfter;
        bool redo;
        bool outOfbalance;
    }

    constructor() Ownable(msg.sender) {
        _operators.add(msg.sender);
        _operators.add(address(this));
    }

    //operators
    modifier onlyOperator() {
        require(_operators.has(msg.sender), "Operators: caller is not the Operator");
        _;
    }

    function addOpts(address[] memory operators) external onlyOwner {
        for (uint256 i = 0; i < operators.length; ++i) {
            if (!isOperator(operators[i])) _operators.add(operators[i]);
        }
    }

    function removeOperators(address operator) external onlyOwner {
        _operators.remove(operator);
    }

    function isOperator(address operator) public view returns (bool) {
        return _operators.has(operator);
    }

    //golden config
    function setGoldenCfg(address token, uint256 tolerance) external onlyOwner {
        goldenSectionCfg[token] = tolerance;
    }

    //manager
    function setVault(address _new) external onlyOwner {
        vault = _new;
    }

    function getBalance(address token) public view returns (uint256) {
        return IERC20(token).balanceOf(address(this));
    }

    function getBalanceOf(address holder, address token) public view returns (uint256) {
        return IERC20(token).balanceOf(holder);
    }

    function withdrawToEx(address token, address to, uint256 amount) public onlyOwner {
        IERC20(token).transfer(to, amount);
    }

    function withdrawAllEx(address token) external onlyOwner {
        IERC20(token).transfer(msg.sender, IERC20(token).balanceOf(address(this)));
    }

    function withdrawAllToEx(address[] memory tokens, address to) external onlyOwner {
        for (uint256 i; i < tokens.length; i++) {
            IERC20 token = IERC20(tokens[i]);
            uint256 balance = token.balanceOf(address(this));
            if (balance > 0) {
                token.transfer(to, balance);
            }
        }
    }

    function withdrawAllEthEx() external payable onlyOwner {
        payable(msg.sender).transfer(address(this).balance);
    }

    function setLogicContact(address _new) external onlyOwner {
        logicContact = _new;
    }

    function setRouter(address _new) external onlyOwner {
        uniSwapRouter = _new;
    }

    /**
     * feed begin **************
     */
    function checkFeed(FeedList[] memory feedList) public view returns (FeedList[] memory, uint256) {
        uint256 total = 0;
        for (uint256 i; i < feedList.length; i++) {
            uint256 balance = feedList[i].addr.balance;
            feedList[i].balance = balance;
            if (balance < feedList[i].min) {
                uint256 amount = feedList[i].max - balance;
                total = amount + total;
                feedList[i].feedAmount = amount;
            }
        }
        return (feedList, total);
    }

    function feed(FeedList[] calldata feedList, address eth, uint256 total) external payable onlyOperator {
        uint256 balance = getBalance(eth);
        require(balance > total, "not enough balance");
        IERC20(eth).withdraw(total);
        for (uint256 i; i < feedList.length; i++) {
            //(bool sent, bytes memory data)
            (bool sent,) = feedList[i].addr.call{value: feedList[i].feedAmount}("");
            require(sent, "Failed to send Ether");
        }
    }
    /**
     * feed end **************
     */

    function _delegate(address implementation) public payable onlyOperator {
        require(implementation != address(0), "error logic address");
        assembly {
            calldatacopy(0, 0, calldatasize())
            let result := delegatecall(gas(), implementation, 0, calldatasize(), 0, 0)
            returndatacopy(0, 0, returndatasize())

            switch result
            case 0 { revert(0, returndatasize()) }
            default { return(0, returndatasize()) }
        }
    }

    receive() external payable {}

    fallback() external payable {
        _delegate(logicContact);
    }
}
