# 项目名称

Forge Test

## 项目简介

本项目是一个基于 Foundry 工具链的 Solidity 智能合约项目，旨在开发和测试以太坊应用程序。项目核心包含一个名为 `ProxyEx` 的代理合约，该合约实现了多种与 Uniswap V2 系列流动性池交互的功能，如套利（Arbitrage）、MEV 检查、流动性池信息获取等。其设计目标是作为一个高效、安全的工具，用于自动化交易策略和链上数据分析。

## 核心依赖

- **Foundry**: 用于构建、测试和部署的高速模块化工具包。
  - **Forge**: 测试框架。
  - **Cast**: 与 EVM 合约交互的瑞士军刀。
  - **Anvil**: 本地以太坊节点。
- **OpenZeppelin Contracts**: 用于访问标准、经过实战测试的智能合约库，如 `Ownable`, `IERC20` 等。

## 项目配置

- **Solidity 版本**: `^0.8.0` (ProxyEx.sol), `^0.8.13` (其他合约和测试)
- **编译器优化**: 已启用，运行次数为 200。
- **默认源码目录**: `src`
- **默认输出目录**: `out`
- **库目录**: `lib` (包含 `forge-std` 和 `openzeppelin-contracts`)
- **RPC URL**: 配置为 PulseChain 主网节点 (`https://rpc-pulsechain.g4mm4.io`)。

## 核心模块

### 1. `ProxyEx.sol`

这是项目的核心合约，它是一个可升级的代理合约，主要功能包括：

- **权限管理**: 使用 `Ownable` 和自定义 `Roles` 库管理操作员（Operator）。
- **Pair 数据管理**: 缓存和管理 Uniswap V2 系列 pair 的数据（token0, token1, reserves）。
- **Swap 操作**:
  - 支持 Uniswap V1, V2, V2Stable, 以及通过 Router 的 swap。
  - 实现了 `_swapFull` 和 `_swapFullSafe` 方法，用于在多个 pair 之间执行原子交换。
  - `_swapFullSafe` 方法特别处理了包含转账税（Fee-on-transfer）的代币。
- **套利计算 (Pump)**:
  - `findMax`: 使用数学公式计算单跳或多跳路径上的理论最大套利输入量。
  - `findMaxGolden`: 使用黄金分割法在给定范围内寻找最优套利输入量，适用于更复杂的场景。
  - `_pump`: 解码打包的交易数据，执行套利逻辑，并返回结果。
  - `pumpSmart`: 批量执行多个 `_pump` 操作。
- **MEV 检查**:
  - `batchCheckMev`: 检查给定路径在正向和反向 swap 时的 gas 消耗和 fee。
  - `batchCheckPairFee`: 检查单个 pair 的实际交易费用。
- **辅助功能**:
  - `batchGetPairInfo`, `batchGetRouterPairs`: 从链上批量获取 pair 和 router 信息。
  - `getAmountOutsByPairsFull`: 计算给定输入量和路径的输出量。
  - `decodePacked`: 解码压缩的 PumpData。
- **资金管理**:
  - `withdrawToEx`, `withdrawAllEx` 等函数用于资金提取。
  - `getBalance`: 查询合约中特定代币的余额。

### 2. 其他 `src/` 合约

- `Counter.sol`: 一个简单的计数器合约，可能是示例或用于测试。
- `FakePair.sol`, `ProxyExBase.sol`, `ProxyEx.sol`: 与核心 `ProxyEx` 功能相关的其他合约或基类。

### 3. `test/` 目录

包含针对 `ProxyEx` 合约的测试用例，例如 `TestPls.sol`。这些测试用例验证了合约的各种功能，包括：

- 套利 (pump)。
- MEV 检查 (checkMevs)。
- Pair 费用检查 (batchCheckPairFee)。
- 合约初始化和基本操作。

## 使用说明

1. **安装依赖**: 确保已安装 Foundry 并克隆了子模块 (`forge-std`, `openzeppelin-contracts`)。
2. **编译合约**:
   ```shell
   forge build
   ```
3. **运行测试**:
   ```shell
   forge test
   ```
4. **格式化代码**:
   ```shell
   forge fmt
   ```
5. **部署与交互**: 使用 `forge script` 部署合约，使用 `cast` 与合约交互。