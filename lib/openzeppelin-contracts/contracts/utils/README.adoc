= Utilities

[.readme-notice]
NOTE: This document is better viewed at https://docs.openzeppelin.com/contracts/api/utils

Miscellaneous contracts and libraries containing utility functions you can use to improve security, work with new data types, or safely use low-level primitives.

 * {ReentrancyGuard}: A modifier that can prevent reentrancy during certain functions.
 * {Pausable}: A common emergency response mechanism that can pause functionality while a remediation is pending.
 * {SafeCast}: Checked downcasting functions to avoid silent truncation.
 * {Math}, {SignedMath}: Implementation of various arithmetic functions.
 * {Multicall}: Simple way to batch together multiple calls in a single external call.
 * {Create2}: Wrapper around the https://blog.openzeppelin.com/getting-the-most-out-of-create2/[`CREATE2` EVM opcode] for safe use without having to deal with low-level assembly.
 * {EnumerableMap}: A type like Solidity's https://solidity.readthedocs.io/en/latest/types.html#mapping-types[`mapping`], but with key-value _enumeration_: this will let you know how many entries a mapping has, and iterate over them (which is not possible with `mapping`).
 * {EnumerableSet}: Like {EnumerableMap}, but for https://en.wikipedia.org/wiki/Set_(abstract_data_type)[sets]. Can be used to store privileged accounts, issued IDs, etc.

[NOTE]
====
Because Solidity does not support generic types, {EnumerableMap} and {EnumerableSet} are specialized to a limited number of key-value types.
====

== Math

{{Math}}

{{SignedMath}}

{{SafeCast}}

== Cryptography

{{ECDSA}}

{{MessageHashUtils}}

{{SignatureChecker}}

{{MerkleProof}}

{{EIP712}}

== Security

{{ReentrancyGuard}}

{{Pausable}}

== Introspection

This set of interfaces and contracts deal with https://en.wikipedia.org/wiki/Type_introspection[type introspection] of contracts, that is, examining which functions can be called on them. This is usually referred to as a contract's _interface_.

Ethereum contracts have no native concept of an interface, so applications must usually simply trust they are not making an incorrect call. For trusted setups this is a non-issue, but often unknown and untrusted third-party addresses need to be interacted with. There may even not be any direct calls to them! (e.g. `ERC20` tokens may be sent to a contract that lacks a way to transfer them out of it, locking them forever). In these cases, a contract _declaring_ its interface can be very helpful in preventing errors.

{{IERC165}}

{{ERC165}}

{{ERC165Checker}}

== Data Structures

{{BitMaps}}

{{EnumerableMap}}

{{EnumerableSet}}

{{DoubleEndedQueue}}

{{Checkpoints}}

== Libraries

{{Create2}}

{{Address}}

{{Arrays}}

{{Base64}}

{{Strings}}

{{ShortStrings}}

{{StorageSlot}}

{{Multicall}}
