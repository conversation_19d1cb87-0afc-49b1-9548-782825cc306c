// SPDX-License-Identifier: UNLICENSED
pragma solidity ^0.8.13;

import "forge-std/console.sol";
import {Test} from "forge-std/Test.sol";
import {ProxyEx, IERC20, IPair} from "../src/ProxyEx.sol";

contract TestPls is Test {
    ProxyEx public ex;
    address exOnline = ******************************************;
    address weth = ******************************************;
    address dai = ******************************************;

    uint112 constant MAX_UINT112 = 5192296858534827628530496329220095;

    function setUp() public {
        ex = new ProxyEx();
        address[] memory addrs = new address[](1);
        addrs[0] = address(this);

        vm.prank(******************************************);
        ProxyEx(payable(exOnline)).addOpts(addrs);

        ex.addOpts(addrs);
        ex.setGoldenCfg(weth, 1e13);
        ex.setGoldenStep(30, 10000);
        //add pls
        vm.prank(weth, address(ex));
        IERC20(weth).deposit{value: address(weth).balance}(); //获得weth
        uint256 balance = IERC20(weth).balanceOf(weth);
        vm.prank(weth, address(ex));
        IERC20(weth).approve(weth, balance);
        vm.prank(weth, address(ex));
        //payable(weth).transfer(address(weth).balance);
        IERC20(weth).transfer(address(ex), 100_000_000 * 10 * 1e18);
        console.log("init weth: ", ex.getBalance(weth));
        console.log("eth: ", address(ex).balance);

        vm.prank(dai, address(ex));
        IERC20(dai).transfer(address(ex), 5000 * 1e18);
        console.log("exonline init dai: ", IERC20(dai).balanceOf(address(ex)));
    }

    function test_honey() public {
        address honeyPair = ******************************************;
        uint256 initBalance = ex.getBalance(weth);

        vm.prank(address(ex));
        IERC20(weth).transfer(honeyPair, 1000000 * 1e18);

        IPair(honeyPair).skim(address(ex));
        uint256 afterBalance = ex.getBalance(weth);
        if (afterBalance > initBalance) {
            console.log("honey delta: ", afterBalance - initBalance);
        } else {
            console.log("fail to honey: ", initBalance - afterBalance);
        }
    }

    function GetRouterPair() public view {
        address router = ******************************************;
        //ProxyEx(payable(exOnline)).batchGetRouterPairs(router, 20, 21);
        ex.batchGetRouterPairs(router, 20, 30);
    }
    /*
    function FindMaxGolden() public returns (uint256 amoutOut, uint256 gas1, uint256 gas2) {
        ProxyEx.PairData[] memory p = new ProxyEx.PairData[](2);

        ProxyEx.PairData memory d0;
        d0.addr = ******************************************; //WPLS-DAI
        d0.fee = 0;
        d0.fp = 99700;
        d0.version = 2;
        p[0] = d0;

        ProxyEx.PairData memory d1;
        d1.addr = ******************************************; //WPLS-DAI
        d1.fee = 0;
        d1.fp = 99700;
        d1.version = 2;
        p[1] = d1;

        ProxyEx.PairDataFull[] memory pFull = ex.pairDatasToFull(p);
        pFull[0].r1 = pFull[0].r1 + 100000 * 1e18;

        ex.getAmountOutsByPairsFull(weth, 1000000 * 1e18, pFull);

        uint256 gas = gasleft();


        //return amountOut;
        amoutOut = ex.findMaxGolden(weth, ex.getBalance(weth), pFull);
        gas2 = gas - gasleft();
        //amoutOut = ex.findMax(weth, pFull);
    }
    */

    function pump() public {
        ProxyEx eOnline = ProxyEx(payable(exOnline));

        ProxyEx.PairData[] memory p = new ProxyEx.PairData[](4);

        ProxyEx.PairData memory d0;
        d0.addr = ******************************************; //dai-pusd
        d0.fee = 0;
        d0.fp = 99710;
        d0.version = 2;
        p[0] = d0;

        ProxyEx.PairData memory d1;
        d1.addr = ******************************************; //希望-pusd
        d1.fee = 0;
        d1.fp = 99710;
        d1.version = 2;
        p[1] = d1;

        ProxyEx.PairData memory d2;
        d2.addr = ******************************************; //希望-usdt
        d2.fee = 0;
        d2.fp = 99710;
        d2.version = 2;
        p[2] = d2;

        ProxyEx.PairData memory d3;
        d3.addr = ******************************************; //usdt-dai
        d3.fee = 0;
        d3.fp = 99700;
        d3.version = 2;
        p[3] = d3;

        ProxyEx.PairData[] memory pre = new ProxyEx.PairData[](1);
        pre[0] = d3;
        ex._swapFullSafe(dai, 5000 * 1e18, ex.pairDatasToFull(pre)); //把第二个池买贵。给后面搬砖

        ex.findMax(dai, ex.pairDatasToFull(p));

        ProxyEx.PumpData memory data = ProxyEx.PumpData({
            convertEth: 0,
            tokenIn0or1: 0,
            calc: 0,
            gasLimit: 200000,
            cost: 0,
            amountIn: 2 * 1e18,
            pairs: p
        });
        eOnline._pump(data);
    }

    function decodePack() public view {
        //bytes memory b =
        //hex"000005d6f994b089259573000000000000000000000000000000000001596d0200000185a62f7a5a160ec87aab1ffc2ea672a5537e0e33616b02000001857e5cebb68fd22a385b039efd50db6b2597bd425bf100000000000000000000000000";

        bytes memory b =
            hex"000000000000000000000000000000000000000000000000000000000000002000000000000000000000000000000000000000000000000000000000000000870000000692f600000a7eaa17d63dd13fc2000000000000000000000000000002000001857ee56043671df55de5cdf8459710433c10324de0ae020000018574ad233ce1826004e7e707b51d27158788a460e02002000001857ec045cb4c237c40b4754191002968d41a028af5aa02138801857e059c74989355cacf939f4a1b0a75dba23c8d6fc800000000000000000000000000000000000000000000000000";
        ex.decodePacked(b);
        /*
        ProxyEx.PumpData memory pd = ex.decodePacked(b);
        pd.convertEth = 1;
        pd.tokenIn0or1 = 1;
        */
        /*
        ProxyEx.PairData[] memory p = new ProxyEx.PairData[](1);
        p[0] = ProxyEx.PairData({addr: ******************************************, fee: 0, fp: 99700, version: 2});
        ProxyEx.PumpData memory pd = ProxyEx.PumpData({
            convertEth: 0,
            tokenIn0or1: 0,
            calc: 1,
            gasLimit: 100,
            cost: 100,
            amountIn: 10000,
            pairs: p
        });

        bytes memory packedPumpData =
            abi.encodePacked(pd.convertEth, pd.tokenIn0or1, pd.calc, pd.gasLimit, pd.cost, pd.amountIn);
        for (uint256 i; i < pd.pairs.length; i++) {
            bytes memory m = abi.encodePacked(pd.pairs[i].version, pd.pairs[i].fee, pd.pairs[i].fp, pd.pairs[i].addr);
            packedPumpData = abi.encodePacked(packedPumpData, m);
        }
        return packedPumpData;
        */
    }
    //reward, status

    event Result(uint112[], uint8[]);
    event Result2(bytes);
    event PumpResults(PumpResult[], uint112[]);

    enum PumpResult {
        None,
        Success, //1.成功
        SuccessOverlow, //2.成功但是超出流动性
        FindMax0, //3:没有利润
        Balance0, //4:没有钱
        SwapErr, //5:swap出错
        FindMaxErr, //6:计算出有利润，但是实际没有
        RewardTooLow //7:有利润但是不能覆盖gas损失
    }

    function eventGas() public {
        uint256 len = 3;
        uint256 gas = gasleft();
        uint112[] memory r1 = new uint112[](len);
        //uint8[] memory r2 = new uint8[](10);
        PumpResult[] memory r3 = new PumpResult[](len);

        for (uint8 i = 0; i < r1.length; i++) {
            r1[i] = i;
            //r2[i] = i + i;
            //r2[i] = uint8(PumpResult(1));
            r3[i] = PumpResult(1);
        }
        //emit Result(r1, r2);
        emit PumpResults(r3, r1);
        /*
        bytes memory b;
        for (uint256 i = 0; i < r1.length; i++) {
            b = abi.encodePacked(b, r1[i], r2[i]);
        }
        emit Result2(b);
        */
        gas - gasleft();
    }

    function checkMevs() public returns (ProxyEx.CheckMevsResultDesc[] memory) {
        ProxyEx.CheckMevsInputDesc[] memory d = new ProxyEx.CheckMevsInputDesc[](1);
        //第一条
        ProxyEx.PairData[] memory p0 = new ProxyEx.PairData[](3);
        ProxyEx.PairData memory p00 =
            ProxyEx.PairData({addr: 0x321884Bc2a7B2dA400AA25F5325E083f9ddEa68a, fee: 0, fp: 99710, version: 2});
        p0[2] = p00;

        ProxyEx.PairData memory p01 =
            ProxyEx.PairData({addr: 0xd78Cb6B8A03Fffe46F4d20E28285222bec05BeAe, fee: 0, fp: 99710, version: 2});
        p0[1] = p01;

        ProxyEx.PairData memory p02 =
            ProxyEx.PairData({addr: 0x5da3F2B568073Cc04B136E866a44F920603556B4, fee: 0, fp: 99710, version: 2});
        p0[0] = p02;

        ProxyEx.CheckMevsInputDesc memory d0 = ProxyEx.CheckMevsInputDesc({tokenIn0or1: true, pairs: p0});
        d[0] = d0;

        //第二条
        /*
        ProxyEx.PairData[] memory p1 = new ProxyEx.PairData[](2);
        ProxyEx.PairData memory p10 =
            ProxyEx.PairData({addr: 0xF3098211d012fF5380A03D80f150Ac6E5753caA8, fee: 99700, fp: 99700, version: 2});
        p1[0] = p10;

        ProxyEx.PairData memory p11 =
        //ProxyEx.PairData({addr: 0x929b6ac884f0FB450aD037861BE5e5b909bB7bcE, fee0: 99000, fee1: 99000, version: 21});
         ProxyEx.PairData({addr: 0xd346967E8874b9C4Dcdd543A88AE47eE8C8bD21f, fee: 99700, fp: 99700, version: 2});
        p1[1] = p11;
        ProxyEx.CheckMevsInputDesc memory d1 = ProxyEx.CheckMevsInputDesc({tokenIn0or1: true, pairs: p1});
        d[1] = d1;
        */

        ProxyEx.CheckMevsResultDesc[] memory rs = ex.batchCheckMev(d);
        return rs;
    }
}
