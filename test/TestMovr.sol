// SPDX-License-Identifier: UNLICENSED
pragma solidity ^0.8.13;

import "forge-std/console.sol";
import {Test, console2} from "forge-std/Test.sol";
import {ProxyEx, IERC20, IPair} from "../src/ProxyEx.sol";

contract TestMovr is Test {
    ProxyEx public ex;
    address wmovr = 0x40375C92d9FAf44d2f9db9Bd9ba41a3317a2404f;
    address payable online = payable(0xC1B10c33706D787680825FDD42C89789B3fc0B44);

    function setUp() public {
        /*
        ex = new ProxyEx();
        */
    }

    function GetPair() public {
        //v1_router: 0x56F6Ca0a3364Fa3aC9F0E8E9858b2966CdF39d03 (imp)
        //v1_router: 0x2f84b9713a96FB356683de7B44dd2d37658b189d (zenlink)
        //v2_router: 0xAA30eF758139ae4a7f798112902Bf6d65612045f (solar)

        //0x6AE84c1a2E529356BE044Db7D4fE9B375Af3f0A4(pair) 0xFFffFffFA083189f870640b141ae1E882c2b5bad(token) //xcKMA
        //IERC20(0xFFffFffFA083189f870640b141ae1E882c2b5bad).decimals();
        //ex._getPairBaseInfo(0x6AE84c1a2E529356BE044Db7D4fE9B375Af3f0A4);

        address[] memory pairs = new address[](1);
        pairs[0] = 0x2cc54b4A3878e36E1C754871438113C1117a3ad7;
        //ex.batchGetPairInfo(pairs);
        //vm.prank(0x5d892fA20E5D212D822152d672C5aF175a26bbEC);
        //ProxyEx(online).setLogicContact(0x05907f8b303658e406bf8127DcB57D32a4101c0a);
        vm.prank(0x5d892fA20E5D212D822152d672C5aF175a26bbEC);
        ProxyEx(online).batchGetRouterPairs(0xAA30eF758139ae4a7f798112902Bf6d65612045f, 0, 1);
        //ProxyEx(online).batchGetRouterPairs(0x56F6Ca0a3364Fa3aC9F0E8E9858b2966CdF39d03, 0, 1);
    }
}
