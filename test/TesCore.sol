// SPDX-License-Identifier: UNLICENSED
pragma solidity ^0.8.13;

import "forge-std/console.sol";
import {Test, console2} from "forge-std/Test.sol";
import {ProxyEx, IERC20, IPair} from "../src/ProxyEx.sol";

contract TestCore is Test {
    ProxyEx public ex;
    address wcore = 0x40375C92d9FAf44d2f9db9Bd9ba41a3317a2404f;
    address wcore_shdw = 0x191E94fa59739e188dcE837F7f6978d84727AD01;
    /*
    function setUp() public {
        ex = new ProxyEx();
        vm.prank(wcore);
        payable(wcore).transfer(address(this).balance);
        IERC20(wcore).deposit(IERC20(wcore).balanceOf(address(this))); //获得wcore
        vm.prank(wcore_shdw);
        payable(wcore_shdw).transfer(address(this).balance);
        IERC20(wcore_shdw).deposit(IERC20(wcore_shdw).balanceOf(address(this))); //获得wcore_shdw
    }
    */
}
