// SPDX-License-Identifier: UNLICENSED
pragma solidity ^0.8.13;

import "forge-std/console.sol";
import {Test} from "forge-std/Test.sol";
import {ProxyEx, IERC20, IPair} from "../src/ProxyEx.sol";
import {FakePair} from "../src/FakePair.sol";

contract TestOec is Test {
    ProxyEx public ex;
    address exOnline = ******************************************;
    //address weth = ******************************************;
    address usdt = ******************************************;
    address wokt = ******************************************;

    /*
    function setUp() public {
        ex = new ProxyEx();
        address usdt_whale = ******************************************;
        vm.prank(usdt_whale);
        IERC20(usdt).transfer(address(ex), 10 * 1e18);
        vm.prank(usdt_whale);
        IERC20(usdt).transfer(address(this), 20 * 1e18);
        console.log("[setUp] ex usdt balance: ", ex.getBalance(usdt));
        //IERC20(usdt)
        ex.setRouter(******************************************);
    }
    */

    function GetBalanceOf() public view {
        address botAddr = ******************************************;
        uint256 b = ex.getBalanceOf(botAddr, ******************************************);
        console.log("balance: %s", b);
        //assertEq(1, 1);
    }

    function CheckPairFee() public {
        // "0x45AE267070B3f98f3719669976Ed4568a6b29e27" : {fee0:0,   fee1:1500}, //usdt-pho
        // "0x11Deb887c1A26c774eD531b395E1C4840381081E" : {fee0:0,   fee1:1500}, //usdt-pho
        // "0x77fF586cbFdB498C88a9a26C9054aB456c61349c" : {fee0:0,   fee1:5000}, //usdt-qlb

        // "0xB372B5CB5aAA55e60B3351f0ef9d72a34802872e"
        // "0x39bd57b490Af6cd5333490E4d8CC949Ab3187Cde" usdt-usdc

        address addr = 0x089dedbFD12F2aD990c55A2F1061b8Ad986bFF88;
        ProxyEx.PairData[] memory p = new ProxyEx.PairData[](1);
        ProxyEx.PairData[] memory pre_p = new ProxyEx.PairData[](0);
        ProxyEx.PairData memory d0;
        d0.addr = addr;
        d0.fee = 99700;
        d0.fp = 99700;
        d0.version = 2;
        p[0] = d0;
        //pre_p[0] = d0;

        ProxyEx.CheckPairFeeInputDesc[] memory pairs = new ProxyEx.CheckPairFeeInputDesc[](1);
        ProxyEx.CheckPairFeeInputDesc memory p0 =
            ProxyEx.CheckPairFeeInputDesc({tokenIn: usdt, pair: p, prePair: pre_p});
        pairs[0] = p0;
        //pairs[1] = p0;
        //pairs[2] = p0;
        //pairs[3] = p0;
        vm.prank(0xee1E23068358Af52f00F0CaBCe9C7F3F6541f26C);
        ProxyEx(payable(exOnline)).batchCheckPairFee(pairs);
        //ex.batchCheckPairFee(pairs);
    }

    function GetRouterPair() public view {
        address router = ******************************************;
        ex.batchGetRouterPairs(router, 303, 304);
    }

    event PumpReslt(uint8[], uint256, uint16, uint16);

    function gasCost() public returns (uint256) {
        uint8[] memory r = new uint8[](5);
        r[0] = 1;
        r[3] = 2;
        uint256 gasLeft = gasleft();
        emit PumpReslt(r, 10, 10, 10);
        return gasLeft - gasleft();
    }

    function withdrawEth() public payable {
        vm.prank(wokt);
        //payable(address(this)).transfer(1e18);
        IERC20(wokt).transfer(address(this), 1e18);
        IERC20(wokt).deposit{value: 1e18}();
        uint256 balance = IERC20(wokt).balanceOf(address(this));
        console.log("[balance] wokt 1", balance);
        console.log("[balance] okt 1", address(this).balance);
        //IERC20(wokt).approve(wokt, balance);
        //vm.prank(address(this));
        IERC20(wokt).withdraw(1e18);
        console.log("[balance] wokt 2", IERC20(wokt).balanceOf(address(this)));
        console.log("[balance] okt 2", address(this).balance);
    }

    struct PairData {
        address addr;
        uint8 version;
        uint16 fee; //只需记录单个方向的fee ( < 65535)
        uint24 fp; //pair fee ( < 99700 )
    }

    struct PumpData {
        bool convertEth; //自动变换eth
        bool tokenIn0or1;
        uint8 calc; //计算方式 2: dydx 1:golden
        uint24 gasLimit; //gas消耗
        uint88 cost;
        uint112 amountIn;
        PairData[] pairs;
    }

    function AbiEncodePacked() public view returns (ProxyEx.PumpData memory) {
        // 使用 abi.encodePacked 将参数打包成紧凑的字节数组
        PairData[] memory p = new PairData[](3);
        p[0] = PairData({addr: ******************************************, fee: 0, fp: 99700, version: 2});
        p[1] = PairData({addr: ******************************************, fee: 50, fp: 99700, version: 1});
        p[2] = PairData({addr: ******************************************, fee: 10000, fp: 99750, version: 3});
        PumpData memory pd = PumpData({
            convertEth: true,
            tokenIn0or1: true,
            cost: 200_000_000_000_000_000_000,
            gasLimit: 500000,
            calc: 2,
            amountIn: 200_000_000_000_000_000_000_000_000,
            pairs: p
        });

        bytes memory packedPumpData = abi.encodePacked(pd.cost, pd.amountIn, pd.convertEth, pd.tokenIn0or1);
        for (uint256 i; i < pd.pairs.length; i++) {
            bytes memory m = abi.encodePacked(pd.pairs[i].version, pd.pairs[i].fee, pd.pairs[i].fp, pd.pairs[i].addr);
            packedPumpData = abi.encodePacked(packedPumpData, m);
        }
        return ex.decodePacked(packedPumpData);
        //return packedPumpData;
    }

    function checkMevs() public returns (ProxyEx.CheckMevsResultDesc[] memory) {
        ProxyEx.CheckMevsInputDesc[] memory d = new ProxyEx.CheckMevsInputDesc[](2);

        ProxyEx.PairData[] memory p0 = new ProxyEx.PairData[](4);
        ProxyEx.PairData memory p00 =
            ProxyEx.PairData({addr: ******************************************, fee: 99700, fp: 99700, version: 2});
        p0[0] = p00;

        ProxyEx.PairData memory p01 =
            ProxyEx.PairData({addr: ******************************************, fee: 99700, fp: 99700, version: 2});
        p0[1] = p01;

        ProxyEx.PairData memory p02 =
            ProxyEx.PairData({addr: 0xE99df88DA6740C4eAd54d68ED8759CDFAc4d5c6F, fee: 99700, fp: 99700, version: 2});
        p0[2] = p02;

        ProxyEx.PairData memory p03 =
            ProxyEx.PairData({addr: ******************************************, fee: 99700, fp: 99700, version: 2});
        p0[3] = p03;

        ProxyEx.CheckMevsInputDesc memory d0 = ProxyEx.CheckMevsInputDesc({tokenIn0or1: true, pairs: p0});
        d[0] = d0;
        //第二条
        ProxyEx.PairData[] memory p1 = new ProxyEx.PairData[](2);
        ProxyEx.PairData memory p10 =
            ProxyEx.PairData({addr: ******************************************, fee: 99700, fp: 99700, version: 2});
        p1[0] = p10;

        ProxyEx.PairData memory p11 =
        //ProxyEx.PairData({addr: 0x929b6ac884f0FB450aD037861BE5e5b909bB7bcE, fee0: 99000, fee1: 99000, version: 21});
         ProxyEx.PairData({addr: 0xd346967E8874b9C4Dcdd543A88AE47eE8C8bD21f, fee: 99700, fp: 99700, version: 2});
        p1[1] = p11;
        ProxyEx.CheckMevsInputDesc memory d1 = ProxyEx.CheckMevsInputDesc({tokenIn0or1: true, pairs: p1});
        d[1] = d1;

        ProxyEx.CheckMevsResultDesc[] memory rs = ex.batchCheckMev(d);
        return rs;
    }

    function HoneyPair() public {
        address addr = 0x45AE267070B3f98f3719669976Ed4568a6b29e27;
        ProxyEx.PairData[] memory p = new ProxyEx.PairData[](1);
        //ProxyEx.PairData[] memory pre_p = new ProxyEx.PairData[](0);
        ProxyEx.PairData memory d0;
        d0.addr = addr;
        d0.fee = 99700;
        d0.fp = 99700;
        d0.version = 2;
        p[0] = d0;
        //pre_p[0] = d0;
        console.log("pair: token0: %s, token1 :%s", IPair(addr).token0(), IPair(addr).token1());

        //(uint256 fee0, uint256 fee1) = ex.checkPairFee(p, usdt, pre_p);
        //console.log("pair: %s, fee0: %s, fee1: %s", addr, fee0, fee1);

        //(uint256[] memory results) = ex.checkHoneyPair(p, usdt);
        //console.log("results: %s, %s, %s, %s, %s", results[0], results[1], results[2], results[3], results[4]);
        //console.log("results: %s, %s, %s", results[0], results[1], results[2]);

        uint256 amountIn = IERC20(usdt).balanceOf(address(ex));
        console.log("holding usdt: %s", amountIn);
        (ProxyEx.PairDataFull[] memory pairs) = ex.pairDatasToFull(p);
        (address tokenIn,) = ex._swapFullSafe(usdt, amountIn, pairs); //tokenIn = pho
        //自己持有的pho
        amountIn = IERC20(tokenIn).balanceOf(address(ex));
        console.log("holding pho: %s", amountIn);
        //pair持有的pho
        //uint256 pairAmountBefore = IERC20(tokenIn).balanceOf(addr);

        //IERC20(tokenIn).transfer(addr, amountIn);
        ex.withdrawToEx(tokenIn, addr, amountIn);
        //uint256 pairAmountAfter = IERC20(tokenIn).balanceOf(addr);
        (, uint256 r1,) = IPair(addr).getReserves();
        //uint256 amountInDelta = IERC20(tokenIn).balanceOf(addr)-pairAmountBefore;
        uint256 amountInDelta = IERC20(tokenIn).balanceOf(addr) - r1;
        console.log("pair pho delta: %s", amountInDelta);
        (uint256[] memory amounts,,) = ex.getAmountOutsByPairsFull(tokenIn, amountInDelta, pairs);
        console.log("amount outs: %s -> %s", amounts[0], amounts[1]);
        IPair(addr).swap(amounts[1], 0, address(ex), new bytes(0)); //取出usdt
        //IPair(addr).swap(0, amountInDelta, address(ex), new bytes(0));
        //IPair(addr).skim(address(ex));
        //ex._swapFull(addr, amounts, pairs);

        uint256 amountInAfter = IERC20(usdt).balanceOf(address(ex));
        console.log("[after] holding: %s", amountInAfter);
    }
    //重入测试

    function PHOswapCall(address sender, uint256, uint256, bytes calldata) public {
        address addr = 0x45AE267070B3f98f3719669976Ed4568a6b29e27;
        address token0 = IPair(addr).token0();
        address token1 = IPair(addr).token1();
        console.log("phoswapCall : %s", sender);
        console.log("b0: %s, b1: %s", IERC20(token0).balanceOf(address(this)), IERC20(token1).balanceOf(address(this)));
        //uint256 repay = 10e18 * 1000 / uint256(996);
        //IERC20(usdt).transfer(0x45AE267070B3f98f3719669976Ed4568a6b29e27, repay);
        uint256 rBefore = IERC20(token1).balanceOf(addr);
        IERC20(token1).transfer(0x45AE267070B3f98f3719669976Ed4568a6b29e27, IERC20(token1).balanceOf(address(this)));
        uint256 rAfter = IERC20(token1).balanceOf(addr);
        console.log("pair received: %s", rAfter - rBefore);
    }

    function PhoReEntry() public {
        address addr = 0x45AE267070B3f98f3719669976Ed4568a6b29e27;
        IERC20(usdt).transfer(addr, 1e18);
        IPair(addr).swap(0, 1e6, address(this), new bytes(0)); //兑换pho

        IPair(addr).swap(10e18, 0, address(this), "t"); //取出usdt
    }

    receive() external payable {}
}
